# HTML 广告素材违规自动跳转检测工具

基于 Playwright 的 Chromium 浏览器引擎实现的 HTML 广告素材违规自动跳转检测工具。

## 功能特性

### 核心功能
- ✅ 检测 HTML 广告素材中的未经用户交互的自动跳转行为
- ✅ 详细记录跳转发生的原因和触发机制
- ✅ 区分用户主动点击和自动跳转行为
- ✅ 支持多种跳转检测方式

### 检测能力
- **Meta Refresh 跳转**: 检测 `<meta http-equiv="refresh">` 标签
- **JavaScript 跳转**: 检测 `location.href`、`location.assign()`、`location.replace()` 等
- **定时器跳转**: 检测 `setTimeout`、`setInterval` 中的跳转逻辑
- **弹窗检测**: 检测 `window.open()` 调用
- **导航事件**: 监听页面导航和框架导航事件

### 性能优化
- 🚀 屏蔽图片加载，减少网络请求
- 🚀 阻止第三方跟踪脚本（Google Analytics、Facebook Pixel 等）
- 🚀 自定义域名阻止列表
- 🚀 请求拦截和资源优化

## 安装依赖

```bash
# 安装 Node.js 依赖
npm install

# 或使用 Bun
bun install

# 安装 Playwright 浏览器
npx playwright install chromium
```

## 快速开始

### 基本使用

```typescript
import { detectRedirectFromHtml } from './ad-redirect-detector';

// 检测 HTML 内容
const htmlContent = `
  <!DOCTYPE html>
  <html>
  <body>
    <script>
      setTimeout(() => location.href = 'https://malicious-site.com', 3000);
    </script>
  </body>
  </html>
`;

const result = await detectRedirectFromHtml(htmlContent);

console.log('检测结果:', result.hasViolation ? '发现违规' : '正常');
console.log('跳转数量:', result.redirects.length);
console.log('触发器数量:', result.triggers.length);
```

### 高级使用

```typescript
import { AdRedirectDetector } from './ad-redirect-detector';

const detector = new AdRedirectDetector({
  timeout: 8000,           // 检测超时时间
  headless: true,          // 无头模式
  blockImages: true,       // 阻止图片加载
  blockTracking: true,     // 阻止跟踪请求
  blockedDomains: [        // 自定义阻止域名
    'google-analytics.com',
    'facebook.com'
  ]
});

// 检测 HTML 内容
const result = await detector.detectFromHtml(htmlContent);

// 生成详细报告
const report = detector.generateReport(result);
console.log(report);

// 关闭浏览器
await detector.close();
```

## 运行示例

### 运行所有示例
```bash
npm run example
# 或
bun run example
```

### 运行测试套件
```bash
npm run test
# 或
bun run test
```

### 运行原始示例
```bash
npm start
# 或
bun run start
```

## API 文档

### 主要类和接口

#### `AdRedirectDetector`
主要的检测器类。

**构造函数**
```typescript
constructor(config?: DetectionConfig)
```

**主要方法**
- `detectFromHtml(htmlContent: string): Promise<RedirectDetectionResult>`
- `detectFromUrl(url: string): Promise<RedirectDetectionResult>`
- `generateReport(result: RedirectDetectionResult): string`
- `close(): Promise<void>`

#### `DetectionConfig`
检测配置接口。

```typescript
interface DetectionConfig {
  timeout?: number;              // 检测超时时间（毫秒）
  headless?: boolean;            // 是否启用无头模式
  blockImages?: boolean;         // 是否阻止图片加载
  blockThirdPartyScripts?: boolean; // 是否阻止第三方脚本
  blockTracking?: boolean;       // 是否阻止跟踪请求
  blockedDomains?: string[];     // 自定义阻止的域名列表
}
```

#### `RedirectDetectionResult`
检测结果接口。

```typescript
interface RedirectDetectionResult {
  hasViolation: boolean;         // 是否检测到违规跳转
  redirects: RedirectInfo[];     // 跳转详情列表
  detectionTime: number;         // 检测耗时（毫秒）
  blockedRequests: number;       // 被阻止的请求数量
  triggers: TriggerInfo[];       // 检测到的潜在跳转触发器
}
```

### 便捷函数

```typescript
// 检测 HTML 内容
detectRedirectFromHtml(htmlContent: string, config?: DetectionConfig): Promise<RedirectDetectionResult>

// 检测 URL
detectRedirectFromUrl(url: string, config?: DetectionConfig): Promise<RedirectDetectionResult>
```

## 命令行工具

工具提供了便捷的命令行接口：

```bash
# 显示帮助
bun run cli.ts --help

# 检测HTML内容
bun run cli.ts -t html '<html><script>setTimeout(() => location.href="http://bad.com", 1000);</script></html>'

# 检测HTML文件
bun run cli.ts -t file ./test-ad.html

# 检测URL
bun run cli.ts -t url https://example.com/ad.html

# 输出报告到文件
bun run cli.ts -t file ./ad.html -o report.txt

# 详细模式
bun run cli.ts -t file ./ad.html -v
```

### 命令行选项

- `-i, --input <value>`: 输入内容（HTML内容、URL或文件路径）
- `-t, --type <type>`: 输入类型: html, url, file (默认: html)
- `--timeout <ms>`: 检测超时时间，毫秒 (默认: 8000)
- `--no-headless`: 显示浏览器窗口
- `--no-block-images`: 不阻止图片加载
- `--no-block-tracking`: 不阻止跟踪请求
- `-o, --output <file>`: 输出报告到文件
- `-v, --verbose`: 详细输出
- `-h, --help`: 显示帮助信息

### 退出码

- `0`: 检测正常，无违规跳转
- `1`: 检测到违规跳转
- `2`: 程序异常或错误
