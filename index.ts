// 使用 playwright 实现 html 素材检测是否存在自动跳转逻辑
import { chromium } from 'playwright';
import fs from 'fs';

(async () => {
  // 启动浏览器
  const browser = await chromium.launch({ headless: false, slowMo: 50 });
  const context = await browser.newContext();
  const page = await context.newPage();
  // 访问百度
  await page.goto('https://www.baidu.com');
  await page.waitForTimeout(10000);
  const content = await page.content();
  // 写入文件，时间戳命名
  fs.writeFileSync(`dist/${Date.now()}.html`, content);
  await browser.close();
})();
