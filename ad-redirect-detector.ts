import { chromium } from 'playwright';
import type { Browser, BrowserContext, Page, Request, Response } from 'playwright';
import fs from 'fs';
import path from 'path';

/**
 * 跳转检测结果接口
 */
export interface RedirectDetectionResult {
  /** 是否检测到违规跳转 */
  hasViolation: boolean;
  /** 跳转详情列表 */
  redirects: RedirectInfo[];
  /** 检测耗时（毫秒） */
  detectionTime: number;
  /** 被阻止的请求数量 */
  blockedRequests: number;
  /** 检测到的潜在跳转触发器 */
  triggers: TriggerInfo[];
}

/**
 * 跳转信息接口
 */
export interface RedirectInfo {
  /** 跳转目标URL */
  targetUrl: string;
  /** 跳转发生时间（相对于页面加载开始） */
  timestamp: number;
  /** 跳转类型 */
  type: 'navigation' | 'location_change' | 'meta_refresh' | 'javascript';
  /** 是否为用户主动触发 */
  userInitiated: boolean;
  /** 触发跳转的原因描述 */
  reason: string;
  /** 相关的DOM元素或脚本信息 */
  context?: string;
}

/**
 * 触发器信息接口
 */
export interface TriggerInfo {
  /** 触发器类型 */
  type: 'timer' | 'meta_refresh' | 'location_assignment' | 'window_open' | 'form_submit';
  /** 触发器内容 */
  content: string;
  /** 发现时间 */
  timestamp: number;
  /** 相关元素 */
  element?: string;
}

/**
 * 检测配置接口
 */
export interface DetectionConfig {
  /** 检测超时时间（毫秒），默认8000 */
  timeout?: number;
  /** 是否启用无头模式，默认true */
  headless?: boolean;
  /** 是否阻止图片加载，默认true */
  blockImages?: boolean;
  /** 是否阻止第三方脚本，默认true */
  blockThirdPartyScripts?: boolean;
  /** 是否阻止跟踪请求，默认true */
  blockTracking?: boolean;
  /** 自定义阻止的域名列表 */
  blockedDomains?: string[];
}

/**
 * HTML 广告素材违规自动跳转检测器
 */
export class AdRedirectDetector {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private config: Required<DetectionConfig>;

  constructor(config: DetectionConfig = {}) {
    this.config = {
      timeout: config.timeout ?? 8000,
      headless: config.headless ?? true,
      blockImages: config.blockImages ?? true,
      blockThirdPartyScripts: config.blockThirdPartyScripts ?? true,
      blockTracking: config.blockTracking ?? true,
      blockedDomains: config.blockedDomains ?? [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com',
        'doubleclick.net',
        'googlesyndication.com',
        'amazon-adsystem.com',
        'adsystem.amazon.com'
      ]
    };
  }

  /**
   * 初始化浏览器
   */
  private async initBrowser(): Promise<void> {
    this.browser = await chromium.launch({
      headless: this.config.headless,
      args: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-dev-shm-usage',
        '--no-sandbox'
      ]
    });

    this.context = await this.browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });
  }

  /**
   * 设置请求拦截
   */
  private async setupRequestInterception(page: Page): Promise<{ blockedCount: number }> {
    let blockedCount = 0;

    await page.route('**/*', (route, request) => {
      const url = request.url();
      const resourceType = request.resourceType();

      // 阻止图片请求
      if (this.config.blockImages && resourceType === 'image') {
        route.abort();
        blockedCount++;
        return;
      }

      // 阻止第三方跟踪脚本
      if (this.config.blockTracking || this.config.blockThirdPartyScripts) {
        const domain = new URL(url).hostname;
        if (this.config.blockedDomains.some(blocked => domain.includes(blocked))) {
          route.abort();
          blockedCount++;
          return;
        }
      }

      // 阻止已知的跟踪请求
      if (this.config.blockTracking) {
        const trackingPatterns = [
          /\/collect\?/,
          /\/analytics/,
          /\/tracking/,
          /\/pixel/,
          /\/beacon/,
          /gtag|gtm|ga\.js/
        ];

        if (trackingPatterns.some(pattern => pattern.test(url))) {
          route.abort();
          blockedCount++;
          return;
        }
      }

      route.continue();
    });

    return { blockedCount: 0 };
  }

  /**
   * 检测HTML内容中的违规跳转
   */
  async detectFromHtml(htmlContent: string): Promise<RedirectDetectionResult> {
    const startTime = Date.now();
    const redirects: RedirectInfo[] = [];
    const triggers: TriggerInfo[] = [];
    let blockedRequests = 0;

    try {
      if (!this.browser) {
        await this.initBrowser();
      }

      const page = await this.context!.newPage();
      
      // 设置请求拦截
      const { blockedCount } = await this.setupRequestInterception(page);
      
      // 监听导航事件
      let userInteracted = false;
      
      page.on('framenavigated', (frame) => {
        if (frame === page.mainFrame()) {
          const currentUrl = frame.url();
          if (!currentUrl.startsWith('data:') && !currentUrl.startsWith('about:')) {
            redirects.push({
              targetUrl: currentUrl,
              timestamp: Date.now() - startTime,
              type: 'navigation',
              userInitiated: userInteracted,
              reason: userInteracted ? '用户主动导航' : '自动跳转导航',
              context: `Frame navigation to ${currentUrl}`
            });
          }
        }
      });

      // 监听用户交互
      page.on('click' as any, () => { userInteracted = true; });
      page.on('keydown' as any, () => { userInteracted = true; });

      // 注入检测脚本
      await page.addInitScript(`
        // 监听 location 变化
        const originalAssign = window.location.assign;
        const originalReplace = window.location.replace;

        window.location.assign = function(url) {
          window.__redirectDetected = {
            type: 'location_assignment',
            url: url,
            timestamp: Date.now(),
            stack: new Error().stack
          };
          return originalAssign.call(this, url);
        };

        window.location.replace = function(url) {
          window.__redirectDetected = {
            type: 'location_replace',
            url: url,
            timestamp: Date.now(),
            stack: new Error().stack
          };
          return originalReplace.call(this, url);
        };

        // 监听 window.open
        const originalOpen = window.open;
        window.open = function(...args) {
          window.__windowOpenDetected = {
            args: args,
            timestamp: Date.now(),
            stack: new Error().stack
          };
          return originalOpen.apply(this, args);
        };

        // 监听定时器
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;

        window.setTimeout = function(callback, delay, ...args) {
          if (typeof callback === 'string' || callback.toString().includes('location') ||
              callback.toString().includes('window.open') || callback.toString().includes('redirect')) {
            window.__timerRedirectDetected = {
              type: 'setTimeout',
              delay: delay,
              callback: callback.toString(),
              timestamp: Date.now()
            };
          }
          return originalSetTimeout.call(this, callback, delay, ...args);
        };

        window.setInterval = function(callback, delay, ...args) {
          if (typeof callback === 'string' || callback.toString().includes('location') ||
              callback.toString().includes('window.open') || callback.toString().includes('redirect')) {
            window.__timerRedirectDetected = {
              type: 'setInterval',
              delay: delay,
              callback: callback.toString(),
              timestamp: Date.now()
            };
          }
          return originalSetInterval.call(this, callback, delay, ...args);
        };
      `);

      // 加载HTML内容
      await page.setContent(htmlContent, { waitUntil: 'domcontentloaded' });

      // 检查页面中的 meta refresh 标签（在等待之前）
      try {
        const metaRefreshResult = await page.evaluate(() => {
          const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
          if (metaRefresh) {
            return {
              type: 'meta_refresh',
              content: metaRefresh.getAttribute('content') || '',
              timestamp: Date.now(),
              element: metaRefresh.outerHTML
            };
          }
          return null;
        });

        if (metaRefreshResult) {
          triggers.push(metaRefreshResult as TriggerInfo);
        }
      } catch (error) {
        console.warn('检查 meta refresh 时出错:', error);
      }

      // 等待指定时间以检测自动跳转
      await page.waitForTimeout(this.config.timeout);

      // 检查页面中的潜在跳转触发器
      let pageTriggersResult: any[] = [];
      try {
        pageTriggersResult = await page.evaluate(() => {
          const triggers: any[] = [];

          // 检查检测到的跳转
          if ((window as any).__redirectDetected) {
            const detected = (window as any).__redirectDetected;
            triggers.push({
              type: 'location_assignment',
              content: `Redirect to: ${detected.url}`,
              timestamp: detected.timestamp,
              element: detected.stack
            });
          }

          if ((window as any).__windowOpenDetected) {
            const detected = (window as any).__windowOpenDetected;
            triggers.push({
              type: 'window_open',
              content: `Window.open called with: ${JSON.stringify(detected.args)}`,
              timestamp: detected.timestamp,
              element: detected.stack
            });
          }

          if ((window as any).__timerRedirectDetected) {
            const detected = (window as any).__timerRedirectDetected;
            triggers.push({
              type: 'timer',
              content: `Timer redirect: ${detected.callback}`,
              timestamp: detected.timestamp,
              element: `${detected.type}(${detected.delay}ms)`
            });
          }

          return triggers;
        });
      } catch (error) {
        console.warn('检查页面触发器时出错:', error);
        // 如果页面已经导航走了，我们仍然可以继续
      }

      triggers.push(...pageTriggersResult);
      blockedRequests = blockedCount;

      await page.close();

      const detectionTime = Date.now() - startTime;
      const hasViolation = redirects.some(r => !r.userInitiated) || triggers.length > 0;

      return {
        hasViolation,
        redirects,
        detectionTime,
        blockedRequests,
        triggers
      };

    } catch (error) {
      console.error('检测过程中发生错误:', error);
      return {
        hasViolation: false,
        redirects,
        detectionTime: Date.now() - startTime,
        blockedRequests,
        triggers
      };
    }
  }

  /**
   * 检测URL中的违规跳转
   */
  async detectFromUrl(url: string): Promise<RedirectDetectionResult> {
    try {
      // 首先获取URL的HTML内容
      if (!this.browser) {
        await this.initBrowser();
      }

      const page = await this.context!.newPage();
      await page.goto(url, { waitUntil: 'domcontentloaded' });
      const htmlContent = await page.content();
      await page.close();

      // 然后使用HTML检测方法
      return await this.detectFromHtml(htmlContent);
    } catch (error) {
      console.error('从URL检测时发生错误:', error);
      return {
        hasViolation: false,
        redirects: [],
        detectionTime: 0,
        blockedRequests: 0,
        triggers: []
      };
    }
  }

  /**
   * 关闭浏览器
   */
  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
    }
  }

  /**
   * 生成检测报告
   */
  generateReport(result: RedirectDetectionResult): string {
    const report = [];
    report.push('=== HTML 广告素材违规跳转检测报告 ===\n');
    
    report.push(`检测结果: ${result.hasViolation ? '发现违规跳转' : '未发现违规跳转'}`);
    report.push(`检测耗时: ${result.detectionTime}ms`);
    report.push(`阻止请求数: ${result.blockedRequests}`);
    report.push('');

    if (result.redirects.length > 0) {
      report.push('检测到的跳转行为:');
      result.redirects.forEach((redirect, index) => {
        report.push(`${index + 1}. ${redirect.userInitiated ? '[用户触发]' : '[自动跳转]'} ${redirect.type}`);
        report.push(`   目标URL: ${redirect.targetUrl}`);
        report.push(`   发生时间: ${redirect.timestamp}ms`);
        report.push(`   原因: ${redirect.reason}`);
        if (redirect.context) {
          report.push(`   上下文: ${redirect.context}`);
        }
        report.push('');
      });
    }

    if (result.triggers.length > 0) {
      report.push('检测到的潜在跳转触发器:');
      result.triggers.forEach((trigger, index) => {
        report.push(`${index + 1}. [${trigger.type}] ${trigger.content}`);
        if (trigger.element) {
          report.push(`   相关元素: ${trigger.element}`);
        }
        report.push('');
      });
    }

    return report.join('\n');
  }
}

/**
 * 便捷函数：检测HTML内容
 */
export async function detectRedirectFromHtml(
  htmlContent: string, 
  config?: DetectionConfig
): Promise<RedirectDetectionResult> {
  const detector = new AdRedirectDetector(config);
  try {
    const result = await detector.detectFromHtml(htmlContent);
    return result;
  } finally {
    await detector.close();
  }
}

/**
 * 便捷函数：检测URL
 */
export async function detectRedirectFromUrl(
  url: string, 
  config?: DetectionConfig
): Promise<RedirectDetectionResult> {
  const detector = new AdRedirectDetector(config);
  try {
    const result = await detector.detectFromUrl(url);
    return result;
  } finally {
    await detector.close();
  }
}
